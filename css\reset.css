html,body,div,dl,dt,dd,ul,ol,li,h1,h2,h3,h4,h5,h6,img,input,p,th,td,textarea,a{
	margin: 0;
	padding: 0;
	box-sizing: border-box;
}
*{
	behavior: url("../other/PIE.htc");
}
table{  
	border-collapse:collapse;  
	border-spacing:0;  
}
body{
	font-family: 'Microsoft Yahei';
	font-size: 15px;
}
img{
	border: none;
}
button{
	cursor: pointer;
}
li{
	list-style: none;
}
i{
	font-style: normal;
}
.clearfix:after{
	clear: both;
	content: '';
	display: block;
}
.clearfix{
	zoom:1;
}
.fl{
	float: left;
}
.fr{
	float: right;
}
a{
	text-decoration: none;
	color: #666;
}
input[type=button],input[type=text], input[type=submit], input[type=file], button,select{ cursor: pointer; -webkit-appearance: none; background: #fff;border-radius: 0;}
::-webkit-input-placeholder { /* WebKit, Blink, Edge */
    color:#525252;
	font-size: 15px;
}
a{
	cursor: pointer;
}
:-moz-placeholder { /* Mozilla Firefox 4 to 18 */
	color:#525252;
	font-size: 15px;
}
::-moz-placeholder { /* Mozilla Firefox 19+ */
	color:#525252;
	font-size: 15px;
}
:-ms-input-placeholder { /* Internet Explorer 10-11 */
	color:#525252;
	font-size: 15px;
}
select{
	outline:none;
	/*Chrome和Firefox里面的边框是不一样的，所以复写了一下*/
	border: solid 1px #000;
	/*很关键：将默认的select选择框样式清除*/
	appearance:none;
	-moz-appearance:none;
	-webkit-appearance:none;
	/*将背景改为红色*/
	background:transparent;
	/*加padding防止文字覆盖*/
	padding-right: 14px;
}
input[type=text]{
	outline:none;
}
/*清除ie的默认选择框样式清除，隐藏下拉箭头*/
select::-ms-expand { display: none; }
.row{
	width: 1200px;
	margin: 0 auto;
	position: relative;
	/* padding: 38px 0 38px; */
}
.container{
	width: 1200px;
	margin: 0 auto;
	position: relative;
}
body{
	min-width: 1200px;
}
