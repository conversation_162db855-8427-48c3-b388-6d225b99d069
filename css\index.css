body{
	background: #c80101;
}
/*top nav*/
.zg_header{
	background: #cf1c22;
	width: 100%;
}
.nav_main {
	padding-top: 14px;
	width: 500px;
	padding-left: 30px;
}
.nav_main li {
	color: #333;
	padding: 0 2px;
	font-size: 16px;
}
.nav_wrap .zx_btn{
	display: block;
	height: 26px;
	font-size: 16px;
	text-align: center;
	line-height: 26px;
	-webkit-border-radius: 14px;
	-moz-border-radius: 14px;
	border-radius: 14px;
	margin: 8px 0 0;
	color: #fff;
}
.nav_wrap{
	padding: 8px 0;
}
/*table*/
table{
	width: 100%;
}
table tr th{
	font-size: 20px;
	background: #cd151b;
	padding: 12px 8px;
	color: #fff;
	border:1px solid #cd151b;
	-webkit-box-shadow: 0 4px 20px #fff;
	-moz-box-shadow: 0 4px 20px #fff;
	box-shadow: 0 4px 20px #fff;
	text-align: center;
}
table tr td{
	font-size: 20px;
	padding: 10px 12px;
	text-align: center;
	border:1px solid #cd151b;
	line-height: 35px;
}
table tr td.lft{
	text-align: left;
}
table td a{
	width: 100px;
	line-height: 33px;
	margin: 0 auto;
	border-radius: 100px;
	display: block;
}
table td a.kc_btn{
	background-color: #e00a0a;
	color: #fff;
}
table td a.zx_btn{
	margin-top: 10px;
	background-color: #ffe330;
	color: #e00a0a;
}
.tab_con a{
	padding: 0 15px;
	margin-right: 15px;
	margin-bottom: 15px;
	line-height: 33px;
	font: bold 24px/50px '微软雅黑';
	background-color: #fff7e4;
	color: #000;
	border-radius: 100px;
	cursor: pointer;
	display: block;
	width: 200px;
    height: 50px;
    text-align: center;
}
.tab_con a.on{
	background-color: #e00a0a;
	color: #fff;
}
.tab_con{
	display: flex;
	justify-content: flex-start;
	flex-wrap: wrap;
	margin-top: 14px;
	justify-content: space-around;
}
.table_wrap .view{
	display: none;
}
.table_title{
	position: relative;
	z-index: 9;
}
/* .zg_banner{
	background: url("../images/bannerbg_01.jpg") no-repeat center top;
	background-size: 1920px 100%;
}
.zg_banner .ban{
	display: block;
} */
img.title{
	display: block;
	margin: 0 auto;
}
.zg_c1{
	background: url("../images/618ban1.jpg") no-repeat center top;
	background-size: 1920px 100%;
	height: 1169px;
	/* padding-top: 30px; */
}
.zg_c1{
	background: url("../images/618ban2.jpg") no-repeat center top;
	background-size: 1920px 100%;
	height: 1169px;
	/* padding-top: 30px; */
}
.bg{background-size: 1920px 100%;}
.border{
	border: 2px solid #c90101;
	border-top: none;
	border-bottom: none;
	padding: 0 32px 0;
	overflow: hidden;
}
.zg_c1 .border{
	
	padding-bottom: 54px;
}
.c-box{
	display: flex;
	justify-content: space-between;
	flex-wrap: wrap;
	margin-top: 60px;
}
.c-box .l{
	width: 481px;
	height: 607px;
	background: url(../images/box_12.png) no-repeat 0 0/100% 100%;
}
.c-box .r{
	width: 617px;
	height: 606px;
	background: url(../images/box_14.png) no-repeat 0 0/100% 100%;
}
.c-box h5{
	font-size: 40px;
	color: #670707;
	line-height: 120px;
	
}
.c-box h5 span{
	color: #cb0101;
}
.c-box .l h5{
	padding-left: 140px;
}
.c-box .r h5{
	padding-left: 210px;
}
.c-box  .i-wrap{
	padding: 14px 14px 0 24px;
}
.c-box  .i-wrap .item{
	display: block;
	margin-bottom: 14px;
}
.c-box .r .item{
	width: 506px;
	height: 115px;
	padding: 26px 38px 16px 114px;
	
}
.c-box .l .item{
	width: 414;
	height: 120px;
	padding: 28px 12px  6px  124px;
}
.c-box .r .item{
	width: 506;
	height: 116px;
	padding: 28px 12px  6px  124px;
}
.c-box .r .item.a1{
	background: url(../images/mk_18.png) no-repeat 0 0/100% 100%;
	margin-top: 30px !important;
}
.c-box .r .item.a2{
	background: url(../images/mk_21.png) no-repeat 0 0/100% 100%;
	margin-top: 60px !important;
}
.c-box .l .item.a1{
	background: url(../images/mk2_03.png) no-repeat 0 0/100% 100%;
	margin-top: 30px !important;
}
.c-box .l .item.a2{
	background: url(../images/mk2_06.png) no-repeat 0 0/100% 100%;
	margin-top: 60px !important;
}
.c-box  .i-wrap .item p{
	color: #fff;
}
.c-box .i-wrap .item .p1{
	font-size: 27px;
	
}
.c-box .i-wrap .item .p2{
	font-size: 16px;
}
.c-box  .item.d{
	padding: 38px 12px  6px  124px !important;
}
.c-box  .i-wrap .item:hover{
	transition: 1s;
	transform: translateY(-6px);
}
.c-box li>a{
	display: block;
	height: 100%;
}
.zg_c2  .row{
	padding-bottom: 30px;
}
.zg_c2 .border{
	/* border: 1px solid #fff; */
	/* border-radius: 0 0 16px 16px; */
	border-top: none;
	/* height: 400px; */
	/* padding-top: 30px; */
	/* padding-bottom: 55px; */
}
/* .zg_c2 .row{
	padding-bottom: ;
} */
.zg_c2  .main-con{
	width: 1132px;
	/* height: 550px; */
	margin: 24px auto 0;
	/* background: url(../images/img_23.jpg) no-repeat center top #fff; */
	overflow: hidden;
	padding-bottom: 60px;
}
.yy-btn{
	width: 196px;
	height: 46px;
	font-size: 20px;
	/* color:#c90014; */
	text-align: center;
	line-height: 43px;
	background: url(../images/btn_03.png) no-repeat 0 0/100% 100%;
	display: block;
	text-indent: -8px;
	/* margin:490px auto 0; */
}
.zg_c3{
	background-color: #fff;
}
.zg_c3 .row{
	padding: 30px 0 80px;
}
.zg_c3 .border{
	
	position: relative;
	padding-bottom: 80px;
	padding-top: 60px;
}
.zg_c3 .details{
	font-size: 22px;
	color: #fff;
	text-align: center;
	width: 1242px;
	height: 68px;
	line-height: 82px;
	position: absolute;
	left: -21px;
	bottom: 0;
	background: url(../images/long_03.png) no-repeat 0 0/100% 100%;
}
.goods{
	display: flex;
	justify-content: space-around;
	flex-wrap: wrap;
}
.goods li{
	width: 364px;
	height: 427px;
	background: url(../images/goods_03.png) no-repeat 0 0/100% 100%;
	text-align: center;
	padding: 56px 20px 0;
}
.goods li h5{
	font-size: 34px;
	color: #fff;
}
.goods li  p{
	font-weight: bold;
	font-size: 38px;
	color: #fff;
}
.goods li div{
	text-align: center;
	width: 100%;
	height: 218px;
}
.left-nav{
	width: 206px;
	height: 553px;
	position: fixed;
	left: 0;
	top: 12%;
	z-index: 9;
	background: url(../images/left_03.png) no-repeat 0 0/100% 100%;
	padding: 30px 30px 0;
}
.left-nav li {
	cursor: pointer;
	text-align: center;
	margin-top: 8px;
	border-bottom: 1px dashed #de0404;
	padding: 12px 0 12px;
}
.left-nav li p{
	font-size: 20px;
	color: #fff;
	text-align: center;
	width: 130px;
	height: 32px;
	line-height: 32px;
	background-color: #de0404;
	margin: 0 auto;
	border-radius: 28px;
}
.left-nav li img{
	width: 74%;
}
.left-nav li:nth-last-child(1){
	border: none;
}
.left-nav .close{
	width: 30px;
	height: 30px;
	color: #de0404;
	border-radius: 50%;
	text-align: center;
	line-height: 30px;
	cursor: pointer;
	background-color: #fff;
	position: absolute;
	left: 50%;
	top: -30px;
}
.win{
	width: 100%;
	height: 100%;
	position: fixed;
	z-index: 99;
	left: 0;
	top: 0;
	display: none;
}
.win .mask{
	width: 100%;
	height: 100%;
	background-color: #000;
	opacity: 0.6;
	left: 0;
	top: 0;
}
.win .form{
	width: 360px;
	left: 50%;
	margin-left: -180px;
	top: 20%;
	background-color: #fff;
	min-height: 200px;
	position: absolute;
	border: 2px solid #a10d01;
	border-radius: 16px;
	padding-bottom: 40px;
}
.win .form-title{
	width: 300px;
	display: block;
	margin:-30px auto 0;
}
.win .form-wrap {
	padding: 0 20px;
}
.win .form-wrap .item{
	width: 262px;
	height: 36px;
	border: 1px solid #de0404;
	border-radius: 26px;
	margin: 14px auto 0;
	display: flex;
	justify-content: flex-start;
	overflow: hidden;
	position: relative;
}
.win .form-wrap .item span.t{
	display: block;
	width: 86px;
	background-color: #de0404;
	color: #fff;
	text-align: center;
	line-height: 34px;
}
.win .form-wrap #getyzm{
	position: absolute;
	right: 0;
	top: 0;
	width: 90px;
	background-color: #de0404;
	color: #fff;
	text-align: center;
	line-height: 34px;
	cursor: pointer;
}
.win .form-wrap .inp{
	width: 160px;
	line-height: 35px;
	border: none;
	padding: 0 8px;
	font-size: 15px;
}
.win .form-wrap select{
	background: url(../images/icon_07.png) no-repeat right center;
	background-size: 18px;
}
.win .form-wrap #submit{
	width: 262px;
	height: 36px;
	border: 1px solid #de0404;
	border-radius: 26px;
	margin: 14px auto 0;
	background-color:#de0404;
	color: #fff;
	text-align: center;
	line-height: 34px;
	cursor: pointer;
	display: block;
}
.policy_label{
	font-size: 12px;
	
}
.policy_label input{
	position: relative;
	top: 2px;
	right: 2px;
}
.win .close{
	width: 30px;
    height: 30px;
    text-align: center;
    line-height: 26px;
    font-size: 18px;
    color: #fff;
    background-color: #de0404;
    position: absolute;
    left: 50%;
    top: -76px;
    cursor: pointer;
    margin-left: -15px;
    border-radius: 50%;
}
.gotop{
	font-size: 18px;
	color: #fff;
}
.bn3{
	background-image: url(../images/banner3.png);
	background-size: cover;
	margin: auto;
	margin-bottom: 100px;
	width: 1200px;
	height: 573px;
}