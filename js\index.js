

$(function(){
    var state = [
        {"state2":"南昌"},{"state2":"赣州"},{"state2":"九江"},
        {"state2":"吉安"},{"state2":"萍乡"},{"state2":"上饶"},
        {"state2":"新余"},{"state2":"抚州"}
      ]
  
    // 抽取不重复的地市，渲染到 #city
    var cities = Array.from(new Set(state.map(item => item.state2)));
    var cityHtml = '<option value="">请选择地市</option>';
    cities.forEach(function(name){
      cityHtml += '<option value="'+ name +'">'+ name +'</option>';
    });
    $('#city').html(cityHtml);
  
    // 弹层打开/关闭
    $('.yyy-btn').on('click', function(){
      $('.win').fadeIn(200);
    });
    $('.win .close').on('click', function(){
      $('.win').fadeOut(200);
    });
  
    // 顶部回到顶部和左侧导航关闭（保留原有）
    $('.gotop').on('click', function(){
      $('html, body').animate({scrollTop:0}, 500);
    });
    $('.left-nav .close').on('click', function(){
      $('.left-nav').fadeOut(200);
    });
  
    // “获取验证码”按钮
    $('#getyzm').on('click', function(){
      if (!$('#isAgree').prop('checked')) {
        alert('未选中用户协议与隐私政策');
        return;
      }
      var phone = $('#phone').val().trim();
      if (!phone) {
        alert('请填写手机号');
        return;
      }
      var reg = /^0?1[3456789]\d{9}$/;
      if (!reg.test(phone)) {
        alert('请输入正确的手机号！');
        return;
      }
      // 假设已有 getSMSCode 函数
      getSMSCode("biaodan", 57146, phone, true, "#getyzm", 120);
    });
  
    // 提交表单
    $('#submit').on('click', function(){
      if (!$('#isAgree').prop('checked')) {
        alert('未选中用户协议与隐私政策');
        return;
      }
      var city = $('#city').val();
      var uname = $('#uname').val().trim();
      var phone = $('#phone').val().trim();
      var yzm   = $('#yzm').val().trim();
  
      if (!city) {
        alert('请选择地市');
        return;
      }
      if (!uname) {
        alert('请输入姓名');
        return;
      }
      if (!phone) {
        alert('请输入手机号');
        return;
      }
      var phone_re = /^1[3-9]\d{9}$/;
      if (!phone_re.test(phone)) {
        alert('请输入正确的手机号');
        return;
      }
      if (!yzm) {
        alert('请输入手机验证码');
        return;
      }
	  
	  var orgn = $.cookie('orgn');
      var owner = $.cookie('owner');
  
      $.ajax({
        url: 'https://zg99.offcn.com/index/biaodan/register?actid=57146&callback=?',
        type: 'GET',
        dataType: 'jsonp',
        jsonp: "callback",
        data: {
          phone:  phone,
          yzm:    yzm,
          city:   city,
          uname:  uname,
          sign:   window.location.href,
          orgn:orgn,
          owner:owner,
          sstimes: new Date(),
          remark: '模考争霸赛',
          isagree: isAgree
        },
        success: function(data){
          if (data.status == "1") {
            $('.win').fadeOut(200);
            alert('提交信息成功!');
          } else {
            alert(data.msg);
          }
        }
      });
    });
  });
  