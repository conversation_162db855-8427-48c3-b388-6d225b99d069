<!DOCTYPE html>
<html lang="en">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1">
        <!--    TDK -->
        <meta name="keywords" content="公务员考试培训,公职考试辅导课程,江西中公教育">
        <meta name="Description" content="2026江西公职模考争霸赛-新大纲、新变化，紧跟国考新大纲，紧扣省考新变化，更多江西公职类考试信息资讯，尽在江西中公教育！ " />
        <title>2026江西公职模考-新大纲、新变化_公务员考试培训_公职考试辅导课程-江西中公教育</title>
        <!--    TDK -->
        <meta http-equiv="Cache-Control" content="no-transform" />
        <meta http-equiv="Cache-Control" content="no-siteapp" />
        <link rel="stylesheet" href="css/swiper-bundle.min.css">
        <link rel="stylesheet" type="text/css" href="css/animate.css">
        <link rel="stylesheet" type="text/css" href="css/reset.css?2">
        <!-- <link rel="stylesheet" type="text/css" href="css/index.css?v26"> -->
        <link rel="stylesheet" href="css/style.css">
        <script src="js/state.js?2"></script>
        <script src="js/jquery.min.js"></script>
        <script src="js/swiper-bundle.min.js"></script>
    <!--引用zg99公共js文件 start-->
    <script src="https://zg99.offcn.com/assets/commjs/zg99_protocol.min.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://zg99.offcn.com/assets/commjs/tj.cookie.min.js"></script>
    <!--引用zg99公共js文件 end-->
        <script type="text/javascript">
            function browserRedirect() {
                var sUserAgent = navigator.userAgent.toLowerCase();
                var bIsIpad = sUserAgent.match(/ipad/i) == "ipad";
                var bIsIphoneOs = sUserAgent.match(/iphone os/i) == "iphone os";
                var bIsMidp = sUserAgent.match(/midp/i) == "midp";
                var bIsUc7 = sUserAgent.match(/rv:*******/i) == "rv:*******";
                var bIsUc = sUserAgent.match(/ucweb/i) == "ucweb";
                var bIsAndroid = sUserAgent.match(/android/i) == "android";
                var bIsCE = sUserAgent.match(/windows ce/i) == "windows ce";
                var bIsWM = sUserAgent.match(/windows mobile/i) == "windows mobile";
                if (bIsIpad || bIsIphoneOs || bIsMidp || bIsUc7 || bIsUc || bIsAndroid || bIsCE || bIsWM) {
                    window.location.href = './m/' + window.location.search;
                } else {}
            }
            // browserRedirect();
        </script>
        <script src="js/iframe-resize.js"></script>
        <!-- <script src="js/headerPc.js"></script> -->
    </head>
    <body>
        <div class="main">
           
            <div class="zg_header">
              <div class="container">
                <div class="nav_wrap clearfix">
                  <a
                    class="logo_wrap fl"
                    href="http://jx.offcn.com"
                    target="_blank"
                    title="江西中公教育"
                  >
                    <img
                      src="./images/logo_03.png"
                      alt="江西中公教育"
                      title="江西中公教育"
                    />
                  </a>
                  <a class="zx_btn fr"  style="font-size: 18px; color:#f3f3f3;padding-top: 10px;">全国统一咨询电话：400-6300-999</a>
                </div>
              </div>
            </div>
          </div>
       
        
        <div class="banner">
            
            <div class="ban1">
                <div class="png1">
                    
                </div>
            </div>
    
            <div class="ban2" id="page1">
                <div class="png2">
                   
                    <div class="btn1">
                         <a href="https://i.offcn.com/c/1820422" target="_blank"> <img src="images/btn1.png" alt=""></a>
                         
                    </div>
                    <div class="btn2">
                        <a href="https://i.offcn.com/c/1820422" target="_blank"> <img src="images/btn1.png" alt=""></a>
                   </div>
                </div>
            </div>
    
            <div class="ban3" id="page2">
                <div class="png3">
                    <div class="btn3">
                        <a href="javascript:;" class="yyy-btn"><img src="images/btn3.png" alt=""></a>
                   </div>
                </div>
            </div>
           
          </div>
    
            <div class="ban4" id="page3">
                <div class="png4">
                    <!-- <div class="btn4">
                        <a href="https://work.weixin.qq.com/kfid/kfc6577a8ee1797c26b?enc_scene=ENC7phMLqUnKHmRGADa1NePWbS8BjBZQwBuPEr9X2hi9cBWi4XUysFF9orcvjjsBpgAh2" target="_blank"> <img src="images/btn4.png" alt=""></a>
                   </div> -->
                   <div class="btn5">
                       <a href="https://i.offcn.com/c/1820625" target="_blank"> <img src="images/btn4.png" alt=""></a>
                  </div>
                </div>
            </div>
        </div>
        
        <!-- <div class="zg_c2" id="md2">
            <div class="row">
                <div class="border">
                    
                    <div class="main-con">
                        <a href="javascript:;" class="yy-btn">线下预约入口</a>
                    </div>
                </div>
            </div>
        </div> -->
        
<!-- 
        <div class="win">
           
            <div class="mask"></div>
            <div class="form">
                <div class="close">x</div>
                <img src="images/form-title_03.png" class="form-title">
                <div class="form-wrap">
                    <div class="item">
                        <span class="t">模考地址</span>
                        <select name="" id="ksxm" class="inp">
                            <option value="">选择地市</option>
                            
                        </select>
                    </div>
                    <div class="item">
                        <span class="t">模考地址</span>
                        <select name="" id="city" class="inp">
                            <option value="">选择地市</option>
                            
                        </select>
                    </div>
                    
                    <div class="item"><span class="t">姓名</span><input type="text" placeholder="输入姓名" class="inp" id="uname"></div>
                    <div class="item"><span class="t">手机号</span><input type="text" placeholder="输入手机号" class="inp" id="phone"></div>
                    <div class="item"><span class="t">验证码</span><input type="text" placeholder="验证码" class="inp" id="yzm"><span id="getyzm">点击获取</span></div>
                   
                    <span id="submit">点击预约</span>
                    <br/>
                    <label for="isAgree" class="policy_label"></label>
                </div>
            </div>
        </div> -->
        <div class="win">
            <div class="mask"></div>
            <div class="form">
              <div class="close">x</div>
              <img src="images/form-title_03.png" class="form-title">
              <div class="form-wrap">
                <div class="item">
                  <span class="t">模考地址</span>
                  <select name="" id="city" class="inp">
                    <option value="">请选择地市</option>
                  </select>
                </div>
                <div class="item">
                  <span class="t">姓名</span>
                  <input type="text" placeholder="输入姓名" class="inp" id="uname">
                </div>
                <div class="item">
                  <span class="t">手机号</span>
                  <input type="text" placeholder="输入手机号" class="inp" id="phone">
                </div>
                <div class="item">
                  <span class="t">验证码</span>
                  <input type="text" placeholder="验证码" class="inp" id="yzm">
                  <span id="getyzm">点击获取</span>
                </div>
                <label style="margin-left: 80px;">
                  <input type="checkbox" id="isAgree"> 我已阅读并同意用户协议与隐私政策
                </label>
                <span id="submit">点击预约</span>
				
              </div>
            </div>
          </div>
        <!--地址 开始-->
        <!--<div class="zg_address">
            <iframe scrolling="no" style="display: block" width="100%" frameborder="0" src="http://jx.offcn.com/zg/jxfxdz/"
             id="myDzIframe"></iframe>
            <script>
                iFrameResize({
                    log: true
                }, '#myDzIframe')
            </script>
        </div>-->
        <!--地址 结束-->
        <div class="index-adder">            
        	<iframe scrolling="no" width="100%" height="752" style="border:none"  src="http://jx.offcn.com/zg/jxfxdz/"></iframe>       
        </div> 
        
        <script src="js/wow.min.js"></script>
        <script type="text/javascript">
            //判断浏览器
            if (!(/msie [6|7|8|9]/i.test(navigator.userAgent))) {
                new WOW().init();
            }
        </script>
        
        <script src="js/index.js"></script>
    <script src="js/replace19Search.js"></script>
    <script src="js/footer.js"></script>
    <script src="js/hljzczx.js"></script>
    </body>
</html>
