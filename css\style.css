

.top {
    width: 1200px;
    height: 60px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    /* background-color: red; */
    align-items: center;
    position: relative;
    z-index: 999
}
.top .logo {
    width: 200px;
}
.zx {
    display: block;
    width: 120px;
    height: 40px;
    border-radius: 40px;
    text-align: center;
    line-height: 40px;
    background: #f6e8a4;
    cursor: pointer;
    color: #333;
}

/* banner */
/* .ban6 {
    display: flex; 
    justify-content: center; 
    align-items: center; 
    
}

.ban6 img {
    width: 100%; 
    height: auto; 
    
} */

.ban1,.ban2,.ban3,.ban4,.ban5,.ban6,.ban7,.ban8,.ban9,.ban10{
    width: 100%;
    height: 100%;
    font-size:0;
}
.main{
    background: url(../images/618ban1.jpg);
    background-repeat: no-repeat;
    background-size: 1920px 100%;
    height: 1169px;
    display: block;
    margin: auto;
    background-position: center;
}
/* .ban1 .png1{
    margin: auto;
    background-image: url(../images/618ban1.jpg);
   height: 1000px;
    background-position: center;

} */
.ban2 .png2{
    margin: auto;
    background-image: url(../images/618ban2.jpg);
   height: 1169px;
    background-position: center;
   /* background-repeat: no-repeat; */
}
.ban2 .png2 .btn1{
    display: block;
    text-align: center;
    position: relative;
    top: 788px;
    right: 353px;
}
.ban2 .png2 .btn2{
    display: block;
    text-align: center;
    position: relative;
    top: 1300px;
    left: 342px;
}


.ban3 .png3{
    margin: auto;
    background-image: url(../images/618ban3.jpg);
   height: 1168px;
    background-position: center;
   background-repeat: no-repeat;
}
.ban3 .png3 .btn3{
    display: block;
    text-align: center;
    position: relative;
    top: 98px;
    left: 343px;
}
/* 定义呼吸动画 */
@keyframes breathing {
    0% {
        transform: scale(1); /* 原始大小 */
    }
    50% {
        transform: scale(1.1); /* 放大到110% */
    }
    100% {
        transform: scale(1); /* 恢复原始大小 */
    }
}

/* 应用到图片 */
.yyy-btn img {
    animation: breathing 1.2s ease-in-out infinite; /* 3秒周期，无限循环 */

}
  /* ============================================= */

.ban4 .png4{
    margin: auto;
    background-image: url(../images/618ban4.jpg);
   height: 832px;
    background-position: center;
   background-repeat: no-repeat;
}
.ban4 .png4 .btn4{
    display: block;
    text-align: center;
    position: relative;
    top: 200px;
}
.ban4 .png4 .btn5{
    display: block;
    text-align: center;
    position: relative;
    top: 524px;
    right: 40px;
}





.ban5 .png5{
    margin: auto;
    background-image: url(../images/618ban5.jpg);
   height: 1000px;
    background-position: center;
   background-repeat: no-repeat;
}
.ban5 .png5 .btn6{
    display: block;
    text-align: center;
    position: relative;
    top: 385px;
}




.ban6 .png6{
    margin: auto;
    background-image: url(../images/618ban6.jpg);
   height: 1000px;
    background-position: center;
   background-repeat: no-repeat;
}
.ban6 .png6 .btn7{
    display: block;
    text-align: center;
    position: relative;
    top: 63px;
}






.ban7 .png7{
    margin: auto;
    background-image: url(../images/618ban7.jpg);
   height: 1000px;
    background-position: center;
   background-repeat: no-repeat;
}
.ban8 .png8{
    margin: auto;
    background-image: url(../images/618ban8.jpg);
   height: 722px;
    background-position: center;
   background-repeat: no-repeat;
}
/* .ban9 .png9{
    margin: auto;
    background-image: url(../images/ban9.png);
   height: 500px;
    background-position: center;
   background-repeat: no-repeat;
}
.ban10 .png10{
    margin: auto;
    background-image: url(../images/ban10.png);
   height: 385px;
    background-position: center;
   background-repeat: no-repeat;
}*/
.ban8 {
    position: relative;
} 

.tb {
    display: flex; /* 使用Flexbox布局 */
    margin: auto;
    justify-content: space-between; /* 图片之间平均分布 */
    width: 72%; /* 宽度设置为100%，使其随容器大小变化 */
    height: auto; /* 高度自动，根据内容调整 */
    position: absolute;
    bottom: 472px; /* 根据需要调整 */
    left: 13.9%; /* 根据需要调整 */
}

.tb2 {
    display: flex; /* 使用Flexbox布局 */
    margin: auto;
    justify-content: space-between; /* 图片之间平均分布 */
    width: 53%; /* 宽度设置为100%，使其随容器大小变化 */
    height: auto; /* 高度自动，根据内容调整 */
    position: absolute;
    bottom: 226px; /* 根据需要调整 */
    left: 25.2%; /* 根据需要调整 */
}

.tb a {
    width: 30%;
	padding: 10px;
	margin: 15px 0;
	/* background-color: #fff; */
	/* border-radius: 10px; */
}

.tb2 a {
    width: 50%;
	padding: 0px;
	margin: 9px 0;
	/* background-color: #fff; */
	/* border-radius: 10px; */
}


.tb img {
    width: 100%; /* 图片宽度设置为100%，使其随容器大小变化 */
    height: auto; /* 高度自动，保持图片比例 */
}

.tb2 img {
    width: 80%; /* 图片宽度设置为100%，使其随容器大小变化 */
    height: auto; /* 高度自动，保持图片比例 */
}

/* .ban6 {
    position: relative;
} */

/* ========================================================================= */
.banner {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    align-items: stretch; /* 或者使用其他合适的对齐方式 */
}


.flex-container { /* 为ul添加flex容器样式 */
    display: flex; /* 设置为flex布局 */
    justify-content: center; /* 子项水平居中 */
    align-items: center; /* 如果需要垂直居中，也可以添加这一行 */
    padding: 0; /* 移除默认的ul内边距 */
    list-style-type: none; /* 移除默认的列表样式 */
    padding-top: 95px;
    margin-right: 0px;
}

.flex-container li { /* 为li添加样式 */
    margin: -108px 5px; /* 为每个li添加左右外边距，可以根据需要调整 */
}

.flex-container li img { /* 如果需要，也可以为图片添加样式 */
    display: block; /* 消除图片底部的空白 */
    max-width: 100%; /* 图片宽度最大为其容器的宽度 */
    height: auto; /* 保持图片的宽高比 */
}

.flex-container2 { /* 为ul添加flex容器样式 */
    display: flex; /* 设置为flex布局 */
    justify-content: center; /* 子项水平居中 */
    align-items: center; /* 如果需要垂直居中，也可以添加这一行 */
    padding: 0; /* 移除默认的ul内边距 */
    list-style-type: none; /* 移除默认的列表样式 */
    padding-top: 227px;
    margin-right: 0px;

}

.flex-container2 li { /* 为li添加样式 */
    margin: -108px 5px; /* 为每个li添加左右外边距，可以根据需要调整 */
}

.flex-container2 li img { /* 如果需要，也可以为图片添加样式 */
    display: block; /* 消除图片底部的空白 */
    max-width: 100%; /* 图片宽度最大为其容器的宽度 */
    height: auto; /* 保持图片的宽高比 */
}




/* ========================弹窗========================== */
.win{
	width: 100%;
	height: 100%;
	position: fixed;
	z-index: 99;
	left: 0;
	top: 0;
	display: none;
}
.win .mask{
	width: 100%;
	height: 100%;
	background-color: #000;
	opacity: 0.6;
	left: 0;
	top: 0;
}
.win .form{
	width: 460px;
    height: 450px;
	left: 50%;
	margin-left: -180px;
	top: 20%;
	background-color: #fff;
	min-height: 200px;
	position: absolute;
	border: 2px solid #a10d01;
	border-radius: 16px;
	padding-bottom: 40px;
}
.win .form-title{
	width: 300px;
	display: block;
	margin:-30px auto 0;
}
.win .form-wrap {
	padding: 0 20px;
}
.win .form-wrap .item{
	width: 320px;
	height: 50px;
	border: 1px solid #0C9BF7;
	border-radius: 26px;
	margin: 14px auto 0;
	display: flex;
	justify-content: flex-start;
	overflow: hidden;
	position: relative;
    margin-bottom: 20px;
}
.win .form-wrap .item span.t{
	display: block;
	width: 86px;
	background-color: #0C9BF7;
	color: #fff;
	text-align: center;
	line-height: 49px;
}
.win .form-wrap #getyzm{
	position: absolute;
	right: 0;
	top: 0;
	width: 90px;
	background-color: #0C9BF7;
	color: #fff;
	text-align: center;
	line-height: 49px;
	cursor: pointer;
}
.win .form-wrap .inp{
	width: 193px;
	line-height: 35px;
	border: none;
	padding: 0 8px;
	font-size: 15px;
}
.win .form-wrap select{
	background: url(../images/icon_07.png) no-repeat right center;
	background-size: 18px;
}
.win .form-wrap #submit{
	width: 262px;
	height: 36px;
	border: 1px solid #0C9BF7;
	border-radius: 26px;
	margin: 14px auto 0;
	background-color:#0C9BF7;
	color: #fff;
	text-align: center;
	line-height: 34px;
	cursor: pointer;
	display: block;
}
.yy-btn{
	width: 196px;
	height: 46px;
	font-size: 20px;
	color:#0C9BF7;
	text-align: center;
	line-height: 43px;
	background: url(../images/btn_03.png) no-repeat 0 0/100% 100%;
	display: block;
	text-indent: -8px;
	/* margin:490px auto 0; */
}
.close{
	width: 45px;
	height: 45px;
	color: #0C9BF7;
	border-radius: 50%;
	text-align: center;
	line-height: 40px;
	cursor: pointer;
	background-color: #fff;
	position: absolute;
	left: 46%;
	top: -93px;
    font-size: 25px;
}

